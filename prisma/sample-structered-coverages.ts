// synthetic data for testing purposes with all possible enums.
export const allEnumCoverages = [
  {
    type: "ADVANCE_COMPENSATION",
    description: "Adelanto de indemnización",
    limit: 1000,
  },
  {
    type: "ASSISTIVE_EQUIPMENT_RENTAL",
    description: "<PERSON><PERSON><PERSON> de sillas de ruedas",
    limit: 200,
  },
  {
    type: "COLLISION_WITH_ANIMALS",
    description: "Colisión con animales cinegéticos",
    limit: 5000,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Capital por fallecimiento",
    limit: 15000,
  },
  {
    type: "EXTRAORDINARY_RISKS_PERSONS",
    description: "Riesgos extraordinarios sobre personas",
    limit: 100000,
  },
  {
    type: "EXTRAORDINARY_RISKS_VEHICLE",
    description: "Riesgos extraordinarios sobre el vehículo",
    limit: 15000,
  },
  { type: "FINES_MANAGEMENT", description: "Gestión de multas de tráfico" },
  {
    type: "FIRE",
    description: "Daños por incendio del vehículo",
    limit: 12000,
  },
  {
    type: "GLASS_BREAKAGE",
    description: "Rotura de lunas sin límite",
    limitIsFullCost: true,
  },
  {
    type: "HANDYMAN_SERVICE",
    description: "Servicio de manitas para pequeñas reparaciones",
  },
  {
    type: "IMMOBILIZATION",
    description: "Inmovilización del vehículo tras accidente",
  },
  { type: "LEGAL_DEFENSE", description: "Defensa jurídica", limit: 2000 },
  {
    type: "LEGAL_REPRESENTATION_EXTENSION",
    description: "Ampliación de defensa jurídica",
    limit: 1500,
  },
  { type: "LICENSE_SUSPENSION", description: "Retirada temporal del carnet" },
  {
    type: "LICENSE_SUSPENSION_SUBSIDY",
    description: "Subsidio diario por retirada de carnet",
    limitPerDay: 30,
    limitMaxMonths: 3,
  },
  {
    type: "LOAD_LIABILITY",
    description: "RC por carga transportada",
    limit: 100000,
  },
  {
    type: "LOST_KEYS",
    description: "Pérdida de llaves del vehículo",
    limit: 250,
  },
  {
    type: "MANDATORY_LIABILITY",
    description: "Responsabilidad civil obligatoria",
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000,
  },
  {
    type: "OTHER",
    customName: "Cobertura experimental",
    description: "Cobertura especial no estandarizada",
    limit: 500,
  },
  {
    type: "PERSONAL_BELONGINGS",
    description: "Efectos personales en el vehículo",
    limit: 1000,
  },
  {
    type: "PSYCHOLOGICAL_ASSISTANCE",
    description: "Asistencia psicológica al conductor",
    limit: 5000,
  },
  {
    type: "REPATRIATION",
    description: "Repatriación de asegurados en el extranjero",
    limit: 2000,
  },
  { type: "THEFT", description: "Robo total o parcial del vehículo" },
  {
    type: "TOTAL_LOSS_DAMAGE",
    description: "Pérdida total por daños",
    limit: 15000,
  },
  {
    type: "TOTAL_LOSS_FIRE",
    description: "Pérdida total por incendio",
    limit: 20000,
  },
  {
    type: "TOTAL_LOSS_THEFT",
    description: "Pérdida total por robo",
    limit: 20000,
  },
  { type: "TOWING_FROM_KM0", description: "Remolque desde el km 0" },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Asistencia en viaje para vehículo y ocupantes",
  },
  {
    type: "VEHICLE_DAMAGE",
    description: "Daños propios con franquicia",
    deductible: 150,
    deductiblePercent: 0.15,
  },
  {
    type: "VEHICLE_REPLACEMENT",
    description: "Vehículo de sustitución",
    limitPerDay: 40,
    limitMaxDays: 7,
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description: "Responsabilidad civil voluntaria",
    limit: 50000000,
  },
  {
    type: "WEATHER_DAMAGE",
    description: "Fenómenos atmosféricos",
    limit: 5000,
  },
  { type: "TYRE_DAMAGE", description: "Daños en neumáticos", limit: 800 },
  {
    type: "MOTORCYCLE_GEAR",
    description: "Cascos y equipamiento del motorista",
    limit: 600,
  },
  {
    type: "NON_STANDARD_ACCESSORIES",
    description: "Accesorios no estándar",
    limit: 1200,
  },
  {
    type: "PET_INJURY",
    description: "Lesiones a mascotas en accidente",
    limit: 1000,
  },
  {
    type: "PASSENGER_ACCIDENTS",
    description: "Accidentes de pasajeros",
    limit: 8000,
  },
  {
    type: "GAP_COVERAGE",
    description: "Cobertura GAP para leasing/financiación",
    limit: 15000,
  },
  {
    type: "PARALYZATION_COMPENSATION",
    description: "Compensación por paralización del vehículo",
    limitPerDay: 25,
    limitMaxDays: 20,
  },
  {
    type: "CARGO_LIABILITY",
    description: "RC por daños a la carga transportada",
    limit: 25000,
  },
  {
    type: "UNAUTHORIZED_USE",
    description: "Uso no autorizado del vehículo",
    limit: 5000,
  },
  { type: "ERROR_REFUELING", description: "Error de repostaje", limit: 400 },
  {
    type: "CHARGING_CABLE",
    description: "Daños en cable de carga eléctrico",
    limit: 300,
  },
  {
    type: "LIABILITY_AT_REST",
    description: "RC en reposo (vehículo estacionado)",
    limit: 100000,
  },
  { type: "TRAILER_LIABILITY", description: "RC por remolque", limit: 100000 },
  {
    type: "THIRD_PARTY_INSOLVENCY",
    description: "Insolvencia de terceros responsables",
    limit: 10000,
  },
  { type: "FREE_WORKSHOP_CHOICE", description: "Libre elección de taller" },
  {
    type: "NEW_VALUE_COMPENSATION",
    description: "Valor a nuevo hasta 24 meses",
    limitMaxMonths: 24,
  },
  {
    type: "HOTEL_EXPENSES_TRAVEL_ASSIST",
    description: "Gastos de hotel en viaje",
    limitPerDay: 70,
    limitMaxDays: 5,
  },
  {
    type: "RESCUE_EXPENSES",
    description: "Gastos de rescate del vehículo",
    limit: 500,
  },
];
// ge_20.pdf — AXA (Motor Elige · Terceros Ampliado)
export const coverages_ge20 = [
  {
    type: 'MANDATORY_LIABILITY',
    description: 'Responsabilidad civil obligatoria',
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000
  },
  {
    type: 'VOLUNTARY_LIABILITY',
    description: 'Responsabilidad civil voluntaria',
    limit: 50000000
  },
  { type: 'LEGAL_DEFENSE', description: 'Protección/defensa jurídica (genérica)' },
  { type: 'GLASS_BREAKAGE', description: 'Lunas (incluida)' },
  { type: 'THEFT', description: 'Robo (incluida)' },
  { type: 'FIRE', description: 'Incendio (incluida)' },
  { type: 'TRAVEL_ASSISTANCE', description: 'Asistencia en viaje (completa)' },
  { type: 'DRIVER_ACCIDENTS', description: 'Accidentes del conductor - fallecimiento', limit: 30000 },
  { type: 'DRIVER_ACCIDENTS', description: 'Accidentes del conductor - invalidez', limit: 30000 }
];
// ge_21.pdf — Mutua Madrileña (Todo Riesgo Plus)
export const coverages_ge21 = [
  {
    type: 'MANDATORY_LIABILITY',
    description: 'Responsabilidad civil obligatoria',
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000
  },
  {
    type: 'VOLUNTARY_LIABILITY',
    description: 'Responsabilidad civil voluntaria',
    limit: 50000000
  },
  { type: 'VEHICLE_DAMAGE', description: 'Daños propios (incl.)' },
  { type: 'FIRE', description: 'Incendio (incl.)' },
  { type: 'THEFT', description: 'Robo (incl.)' },
  { type: 'NON_STANDARD_ACCESSORIES', description: 'Accesorios opcionales incluidos', limit: 500 },
  { type: 'OTHER', customName: 'Reparaciones urgentes', description: 'Reparaciones urgentes por siniestro', limit: 180 },
  { type: 'NEW_VALUE_COMPENSATION', description: 'Valor a nuevo 3 años desde 1ª matriculación', limitMaxMonths: 36 },
  { type: 'DRIVER_ACCIDENTS', description: 'Conductor - muerte', limit: 12021 },
  { type: 'DRIVER_ACCIDENTS', description: 'Conductor - incapacidad permanente', limit: 18031 },
  { type: 'DRIVER_ACCIDENTS', description: 'Conductor - asistencia sanitaria', limit: 1804 },
  { type: 'LEGAL_DEFENSE', description: 'Abogado designado por la compañía (Mutua)', limitIsUnlimited: true },
  { type: 'LEGAL_DEFENSE', description: 'Libre elección de abogado / conflicto de interés', limit: 1000 },
  { type: 'LEGAL_DEFENSE', description: 'Fianzas penales', limit: 6000 },
  { type: 'TRAVEL_ASSISTANCE', description: 'Asistencia en viaje ampliada - reparación in situ y traslado' },
  { type: 'RESCUE_EXPENSES', description: 'Gastos de rescate del vehículo', limit: 180 },
  { type: 'TRAVEL_ASSISTANCE', description: 'Gastos de custodia y depósito', limit: 120 },
  { type: 'HOTEL_EXPENSES_TRAVEL_ASSIST', description: 'Gastos de hotel por incidencia del vehículo (España)', limitPerDay: 60, limitMaxDays: 3 },
  { type: 'TRAVEL_ASSISTANCE', description: 'Asistencia por accidente/enfermedad: repatriación/transportes', limit: 9000 },
  { type: 'REPATRIATION', description: 'Transporte/repatriación de fallecidos y acompañantes', limit: 9000 },
  { type: 'TRAVEL_ASSISTANCE', description: 'Asistencia sanitaria en el extranjero (varios conceptos)', limit: 9000 },
  { type: 'HOTEL_EXPENSES_TRAVEL_ASSIST', description: 'Hotel en el extranjero', limitPerDay: 60, limitMaxDays: 10 },
  { type: 'TRAVEL_ASSISTANCE', description: 'Gastos médicos (copago mínimo 90)', limit: 6000 },
  { type: 'VEHICLE_REPLACEMENT', description: 'Vehículo de sustitución por avería', limitMaxDays: 3 },
  { type: 'VEHICLE_REPLACEMENT', description: 'Vehículo de sustitución por accidente sin pérdida total', limitMaxDays: 10 },
  { type: 'VEHICLE_REPLACEMENT', description: 'Vehículo de sustitución por pérdida total o robo', limitMaxDays: 31 },
  { type: 'PARALYZATION_COMPENSATION', description: 'Indemnización diaria por no prestación', limitPerDay: 30 }
];
// ge_22.pdf — Qualitas Auto (Riesgo combinado)
export const coverages_ge22 = [
  {
    type: 'MANDATORY_LIABILITY',
    description: 'Responsabilidad civil obligatoria',
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000
  },
  {
    type: 'VOLUNTARY_LIABILITY',
    description: 'Responsabilidad civil voluntaria',
    limit: 50000000
  },
  { type: 'PASSENGER_ACCIDENTS', description: 'Fallecimiento ocupantes', limit: 50000 },
  { type: 'PASSENGER_ACCIDENTS', description: 'Invalidez total ocupantes', limit: 100000 },
  { type: 'PASSENGER_ACCIDENTS', description: 'Invalidez parcial ocupantes', limit: 50000 },
  { type: 'PASSENGER_ACCIDENTS', description: 'Gastos asistencia sanitaria', limit: 5000 },
  { type: 'ADVANCE_COMPENSATION', description: 'Anticipo por fallecimiento', limit: 5000 },
  { type: 'TRAVEL_ASSISTANCE', description: 'Asistencia en viaje estándar con remolcaje ilimitado', limitIsUnlimited: true },
  { type: 'LEGAL_DEFENSE', description: 'Defensa jurídica general', limit: 1500 },
  { type: 'LEGAL_DEFENSE', description: 'Fianzas penales', limit: 24000 },
  { type: 'ADVANCE_COMPENSATION', description: 'Adelanto de indemnizaciones', limit: 6000 },
  { type: 'THIRD_PARTY_INSOLVENCY', description: 'Insolvencia de terceros', limit: 12000 },
  { type: 'VEHICLE_REPLACEMENT', description: 'Vehículo de sustitución: robo/accidente (4h MO)', limitIsUnlimited: false }
];
// ge_1.pdf — Hello Auto Flex (Terceros + Lunas)
export const coverages_ge_1 = [
  {
    type: "MANDATORY_LIABILITY",
    description: "Responsabilidad Civil Obligatoria",
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000,
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description: "Responsabilidad Civil Voluntaria",
    limit: 50000000,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Defensa penal con abogado de compañía",
    limitIsUnlimited: true,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Defensa penal libre elección",
    limit: 1000,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Reclamación de daños con abogado de compañía",
    limitIsUnlimited: true,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Reclamación de daños libre elección",
    limit: 1000,
  },
  {
    type: "ADVANCE_COMPENSATION",
    description: "Adelanto de indemnización",
    limit: 6000,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Peritación para reclamar",
    limit: 600,
  },
  {
    type: "RESCUE_EXPENSES",
    description: "Rescate y salvamento",
    limit: 300,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Traslado del vehículo",
    limitIsUnlimited: true,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Reparación en carretera",
    limitIsFullCost: true,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Custodia del vehículo",
    limit: 120,
  },
  {
    type: "HOTEL_EXPENSES_TRAVEL_ASSIST",
    description: "Prolongación de estancia por enfermedad",
    limitPerDay: 80,
    limitMaxDays: 10,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Fallecimiento / Incapacidad permanente del conductor",
    limit: 25000,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Asistencia sanitaria en centros no concertados",
    limit: 3000,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Asistencia sanitaria en centros concertados",
    limitIsUnlimited: true,
  },
  {
    type: "GLASS_BREAKAGE",
    description: "Rotura de lunas",
    limitIsFullCost: true,
  },
];
// ge_2.pdf — Verti (Todo Riesgo con Franquicia)
export const coverages_ge_2 = [
  {
    type: "MANDATORY_LIABILITY",
    description: "Responsabilidad Civil Obligatoria",
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000,
  },
  {
    type: "TRAILER_LIABILITY",
    description: "Responsabilidad civil por remolque y caravanas ligeros",
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Defensa jurídica",
    limit: 600,
  },
  {
    type: "PASSENGER_ACCIDENTS",
    description: "Seguro del conductor: Fallecimiento por accidente",
    limit: 30000,
  },
  {
    type: "PASSENGER_ACCIDENTS",
    description: "Seguro del conductor: Invalidez por accidente",
    limit: 30000,
  },
  {
    type: "PASSENGER_ACCIDENTS",
    description: "Asistencia sanitaria conductor",
    limit: 30000,
  },
  {
    type: "GLASS_BREAKAGE",
    description: "Rotura de lunas y parabrisas",
    limitIsFullCost: true,
  },
  {
    type: "FIRE",
    description: "Incendio del vehículo asegurado",
    limitIsFullCost: true,
  },
  {
    type: "THEFT",
    description: "Robo del vehículo asegurado",
    limitIsFullCost: true,
  },
  {
    type: "COLLISION_WITH_ANIMALS",
    description: "Daños por atropello de especies cinegéticas",
  },
  {
    type: "VEHICLE_DAMAGE",
    description: "Daños propios con franquicia",
    deductible: 150,
  },
  {
    type: "FINES_MANAGEMENT",
    description: "Defensa en multas de tráfico e información al automovilista",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Asistencia en viaje completa",
    limitIsUnlimited: true,
  },
  {
    type: "FREE_WORKSHOP_CHOICE",
    description: "Libre elección de taller",
  },
];
