# Brownfield PRD — Policy Comparison Feature

## 1. Goals and Background Context

The goal of this enhancement is to introduce a **side-by-side policy comparison UI** that allows account holders to easily compare their **current policy** with any **received offer** in the auction system.  

Currently, the platform provides:  
- A **PolicyDetailsDrawer** for viewing policy details.  
- An **Offers table** with premiums, insurers, and a “Comparar” button.  

Gap identified:  
- No direct way to **compare coverages, premiums, and limits** visually between the current policy and received offers.  

Enhancement objectives:  
1. Enable **one-click comparison** of current policy vs offer.  
2. Provide **clear, visual indicators** (green = better, red = worse, dash = missing). For example the current Policy could have missing a coverage that the offer has. 
3. Increase **trust, usability, and conversion** by making differences obvious.  
4. Reuse the existing **PolicyDetailsDrawer** to maintain consistency and minimize implementation effort.  

---

## 2. Requirements

### Functional Requirements
1. **Comparar button action**  
   - From each “Oferta Recibida” row, clicking “Comparar” opens the **PolicyDetailsDrawer** in **compare mode**.  
   - Left column = Current Policy; Right column = Selected Offer.  

2. **Summary comparison**  
   - Fields:  
     - Aseguradora  
     - Prima anual (€) → Lower is better  
     - Cantidad de coberturas → Higher is better  
   - Indicators:  
     - ✅ Green arrow (up) for better  
     - ❌ Red arrow (down) for worse  
     - ➖ Dash for “not available”  

3. **Coverage-level comparison**  
   - Merged by **normalized title**.  
   - For each coverage:  
     - `title`  
     - `limit` (numeric comparison, green = higher, red = lower)  
     - `description`  
   - Missing coverage → “No incluida” badge using ➖.  

4. **Visual feedback**  
   - Indicators at both **summary** and **coverage** level.  
   - Color rules:  
     - Emerald `#3EA050` → Better  
     - Red `#DC2626` → Worse  
     - Neutral grey → Equal / Not available  
     - Lime `#3AE386` highlight for active offer  

5. **Accessibility**  
   - Drawer supports keyboard nav & Esc to close.  
   - Screen readers announce comparison context (“Comparando Póliza Actual vs Oferta Recibida #ID”).  

6. **Performance**  
   - Drawer loads only **two snapshots** (policy + offer) → lightweight.  
   - Comparison logic in frontend, no backend round-trip.  

### Non-Functional Requirements
- **Consistency**: Must reuse existing drawer to avoid fragmenting UX.  
- **Reliability**: Coverage normalization must handle coverage name retrieving from the common enum and using the utily translations for render the names in Spanish.
- **Scalability**: Should render up to 50 coverages without lag.  

---

## 3. User Interface Design Goals

The UI design must prioritize **clarity, speed of comparison, and visual cues** that reduce user cognitive load.  

### Design Objectives
1. **Two-column layout**  
   - Left = Current Policy  
   - Right = Offer Recibida #ID  
   - Fixed grid, scrolls vertically in sync.  

2. **Visual hierarchy**  
   - Summary cards (aseguradora, prima, cantidad de coberturas) displayed first.  
   - Coverage comparison table follows.  

3. **Feedback indicators**  
   - Positive (better offer): Green upward arrow + bold text.  
   - Negative (worse offer): Red downward arrow + bold text.  
   - Missing coverage: Red badge with “No incluida”.  
   - Equal/neutral: Grey text with “Igual”.  

4. **Consistency with Zeeguros Design System**  
   - Use existing drawer component (PolicyDetailsDrawer).  
   - Shadcn UI components: `Card`, `Badge`, `Table`, `Tooltip`.  
   - Colors restricted to palette:  
     - White, Black  
     - Lime `#3AE386` (highlight/CTA)  
     - Emerald `#3EA050` (positive)  
     - Red `#DC2626` (negative)  
     - Grey/muted for neutral.  

5. **Accessibility**  
   - Keyboard navigation fully supported.  
   - Screen reader announces “Comparación entre Póliza Actual y Oferta Recibida #ID”.  
   - Color indicators must also have icons/text to avoid color-only signals.  

6. **Empty & edge states**  
   - If no coverages found on one side → show placeholder text “Sin coberturas registradas”.  
   - If premium data missing → display “No disponible” with tooltip.  

---

## 4. Constraints & Risks

### Constraints
1. **Technical Constraints**  
   - Must reuse existing **PolicyDetailsDrawer** component.  
   - Comparison logic runs **fully client-side** (no new backend endpoints).  

2. **Design Constraints**  
   - Must adhere to Zeeguros UI palette (White, Black, Lime `#3AE386`, Emerald `#3EA050`, Red `#DC2626`).  
   - Must use Shadcn UI components already integrated (Card, Badge, Table, Tooltip).  

3. **Business Constraints**  
   - Feature must not disrupt auction core flow (no additional user steps).  

### Risks

2. **Usability Risk**  
   - Users may find side-by-side drawer overwhelming if >40 coverages.  
   - Mitigation: Add scroll sync, clear section headers, and progressive disclosure.  

3. **Performance Risk**  
   - If coverage list large, rendering may lag.  
   - Mitigation: Virtualize coverage rows when >30 items.  

4. **Visual Clarity Risk**  
   - Color-only indicators may confuse colorblind users.  
   - Mitigation: Always combine icon + text label.  

5. **Adoption Risk**  
   - Brokers may fear comparisons highlight weak offers.  
   - Mitigation: Position feature as **transparency tool** → increases trust in auction.  