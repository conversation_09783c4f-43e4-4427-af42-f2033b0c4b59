In the sample policy, we have 130 sample policies!!! It's a lot, but it help us to understand that we need to enhance two main topics on the @/Users/<USER>/Documents/programming/zeeguros/zee-next-app/src/components/shared/PolicyDetailsDrawer.tsx 

I. The Coberturas accordion should have subcategories that group the coverages for example:
Main coverage groups → enum mapping

1) Civil Liability (RC)
	•	MANDATORY_LIABILITY
	•	VOLUNTARY_LIABILITY
	•	Liability extensions:
LIABILITY_AT_REST, TRAILER_LIABILITY, LOAD_LIABILITY, CARGO_LIABILITY

⸻

2) Legal Defense & Management
	•	LEGAL_DEFENSE
	•	LEGAL_REPRESENTATION_EXTENSION
	•	FINES_MANAGEMENT
	•	LICENSE_SUSPENSION
	•	LICENSE_SUSPENSION_SUBSIDY
	•	THIRD_PARTY_INSOLVENCY (often sold under legal protection / complementary indemnities)

⸻

3) Damage to the Insured Vehicle
	•	VEHICLE_DAMAGE
	•	FIRE
	•	THEFT
	•	GLASS_BREAKAGE
	•	WEATHER_DAMAGE
	•	COLLISION_WITH_ANIMALS
	•	TYRE_DAMAGE
	•	NON_STANDARD_ACCESSORIES
	•	CHARGING_CABLE
	•	ERROR_REFUELING
	•	UNAUTHORIZED_USE
	•	Total-loss variants: TOTAL_LOSS_DAMAGE, TOTAL_LOSS_FIRE, TOTAL_LOSS_THEFT

⸻

4) Travel Assistance
	•	TRAVEL_ASSISTANCE (generic container; you may split into multiple rows for sub-benefits)
	•	TOWING_FROM_KM0
	•	RESCUE_EXPENSES
	•	HOTEL_EXPENSES_TRAVEL_ASSIST
	•	Also related (often under “assistance to persons”): REPATRIATION, IMMOBILIZATION (some insurers list immobilization under assistance)

⸻

5) Personal Accidents
	•	DRIVER_ACCIDENTS
	•	PASSENGER_ACCIDENTS
	•	PSYCHOLOGICAL_ASSISTANCE
	•	PET_INJURY
	•	EXTRAORDINARY_RISKS_PERSONS

⸻

6) Replacement Vehicle / Immobilization
	•	VEHICLE_REPLACEMENT
	•	PARALYZATION_COMPENSATION
	•	HANDYMAN_SERVICE
	•	(If you prefer, you can also keep IMMOBILIZATION here instead of group 4—both placements are common in wordings.)

⸻

7) Valuation & Compensation
	•	NEW_VALUE_COMPENSATION
	•	GAP_COVERAGE
	•	ADVANCE_COMPENSATION
	•	EXTRAORDINARY_RISKS_VEHICLE (sometimes shown under material damage; grouping here is also common when it changes payout basis)

⸻

8) Complementary Services
	•	FREE_WORKSHOP_CHOICE
	•	PERSONAL_BELONGINGS
	•	ASSISTIVE_EQUIPMENT_RENTAL
	•	LOST_KEYS
	•	REPATRIATION (if you didn’t place it in Travel Assistance—choose one home)
	•	OTHER (use with customName only)

Notes:
	•	OTHER should live in Complementary Services by default; if you need analytics grouping, you can re-map based on customName.
	•	REPATRIATION and IMMOBILIZATION float between Assistance vs. Replacement blocks depending on the insurer’s layout; pick one consistently for your reporting.

### II.UI enhancements for the PolicyDetailsDrawer.tsx to support new Coverages and BidCoverages model
We should update the PolicyDetailsDrawer.tsx to support the new model and display the coverages in a more user-friendly way for both policies and bids. 