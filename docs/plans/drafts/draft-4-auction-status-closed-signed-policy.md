Ok. Vamos a trabajar ahora cómo muta la subasta cuando su estado/state es cerrado o "CLOSED".  y su estado "SIGNED_POLICY".

Tab Detalles:
Card: ¿Qué ocurre ahora? 
El mensaje debe cambiar a algo como tu subasta ha finalizado. Descubre quiénes tus ofertas personalizadas ganadoras, recuerda que en la mayor brevedad te van a contactar los agentes. este mensaje debe ser en español y expresarse en bullet points como esta ahora.

El mensaje de ⚡ No necesitas hacer nada más por ahora. Nos encargamos de que recibas las mejores propuestas posibles.
debe actualizarse a algo como, Ahora debes elegir una de tus ofertas personalizadas y en cuanto hayas firmado con tu nuevo agente, recuerda subirla y así quedará guardada en tu wallet de pólizas y será fácil para ti encontrarla cuando te toque renovar el próximo año.

Card: Cronología de la subasta
Debe añadirse un nuevo evento que sea, "Pendiente firmar renovación".
Al darle al colapasable, deben de haber dos tareas pendientes:
Elegir nuevo agente y Cargar nueva póliza renovada.


Tab Ofertas Recibidas
En la tabla de ofertas recibidas, nosotros actualmente tenemos una tabla con las distintas ofertas. Debemos resaltar con un emoji de primero, segundo y tercer lugar al lado de los brokers ganadores. Sus nombres deben verse completos. Actualmente solo se ve el nombre y la inicial de su apellido. 
Estas tres posiciones deben de resaltarse visualmente y todas las demás ofertas, si bien la paginación va a seguir funcionando, se va a desactivar la posibilidad de ver los detalles de las demás ofertas y se deben de verse menos resaltadas visualmente.

La expresión para definir quienes ganan, se basa por: Menor prima anual, mayor cantidad de coberturas incluidas.

El botón de comparar cambia su label a contactar. Y esto va a abrir el mismo Drawer comparador que ya está implementado, pero se va a habilitar una sección arriba al principio que dice detalles de contacto y ahí vamos a encontrar el correo del agente y su número de teléfono. Junto al lado derecho, un botón que dice llamar ya, esto deberá triggerar un tel:[phonenumber]

En este estado también se habilitará la opción de descargar el documento de "cotización" que facilitan los brokers. Podemos reutilizar la UI actual del drawer llamada "Documentos de la Póliza" que ya trae el documento de la póliza actual.

debe haber una sección también para confirmar si este es el broker con el que hemos firmado la póliza, no estoy seguro de como deba ser visualmente (debes ayudarme a definirlo) pero básicamente necesitamos que el usuario confirme con que broker termina firmando la renovación de su póliza de coche o moto y también nos suba ese documento de su nueva póliza, para poder guardarlo en su wallet. Cuando esto ocurra la subasta cambiaría de estado a "SIGNED_POLICY".

Cuando esto ocurre, la sección de detalles:
Póliza actual con su botón ver detalles traería los detalles de esta nueva póliza.
¿Qué ocurre ahora? 
Necesita darle a entender al usuario que ya ha completado el proceso de renovación y agradecer por usar zeeguros para renovar su póliza.
Cronología de la subasta
Marca como completado el evento Pendiente firmar renovación y sus dos sub-tareas.
también se muestra la fecha en que esto ocurrió, que es cuando el usuario sube su nueva póliza y confirma cual es el nuevo agente.