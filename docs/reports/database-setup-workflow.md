# Database Setup Workflow

## Overview

This document describes the unified database setup workflow implemented to resolve dual-migration architecture issues. The system now uses a **Prisma-first approach** with **Supabase infrastructure separation** for complete database reproducibility.

## Architecture Principles

### Prisma Responsibilities (Data Models)
- ✅ All business models (User, Policy, Auction, etc.)
- ✅ Enums and relationships
- ✅ Schema evolution and migrations
- ✅ TypeScript type generation

### Supabase Responsibilities (Infrastructure)
- ✅ PostgreSQL extensions (pg_cron, pg_net)
- ✅ Database functions and stored procedures
- ✅ Cron job scheduling
- ✅ Advanced PostgreSQL features

## Quick Start

### Complete Database Setup
```bash
# One command to set up everything
npm run db:rebuild
```

This command runs the following sequence:
1. **Prisma Migration Reset**: `npm run migrate:reset -- --force`
2. **Infrastructure Setup**: `npm run db:setup-infrastructure`
3. **RLS Policies**: `npm run db:apply-policies`
4. **Seed Data**: `npm run db:seed`

### Individual Components

```bash
# Reset only Prisma migrations
npm run migrate:reset -- --force

# Set up Supabase infrastructure
npm run db:setup-infrastructure

# Individual infrastructure components
npm run db:setup-extensions    # PostgreSQL extensions
npm run db:setup-functions     # Database functions
npm run db:setup-cron         # Cron jobs

# Apply RLS policies
npm run db:apply-policies

# Seed test data
npm run db:seed
```

## File Organization

### Prisma Files
```
prisma/
├── schema.prisma              # Single source of truth for data models
├── migrations/                # Prisma-managed schema evolution
└── seed.ts                   # Test data seeding
```

### Supabase Infrastructure
```
supabase/
├── infrastructure/           # Infrastructure-only files
│   ├── extensions.sql       # PostgreSQL extensions
│   ├── functions.sql        # Database functions
│   └── cron-jobs.sql        # Cron job scheduling
├── policies/                # RLS policies
└── functions/               # Edge functions
```

## Development Workflow

### New Developer Setup
1. Clone repository
2. Copy `.env.example` to `.env.local`
3. Configure database connection
4. Run `npm run db:rebuild`
5. Start development: `npm run dev`

### Schema Changes
1. Modify `prisma/schema.prisma`
2. Run `npx prisma migrate dev --name descriptive_name`
3. Commit both schema and migration files

### Infrastructure Changes
1. Modify files in `supabase/infrastructure/`
2. Run `npm run db:setup-infrastructure`
3. Test changes thoroughly

## Troubleshooting

### Common Issues

#### "Table does not exist" errors
- Run `npm run db:rebuild` to ensure complete setup
- Check that Prisma migrations have been applied

#### Missing TypeScript types
- Run `npx prisma generate` to regenerate types
- Ensure all models are in `prisma/schema.prisma`

#### Cron jobs not working
- Verify extensions are enabled: `npm run db:setup-extensions`
- Check function installation: `npm run db:setup-functions`
- Verify cron scheduling: `npm run db:setup-cron`

### Verification Commands

```bash
# Check database connection
npx prisma db pull

# Verify Prisma types
npx prisma generate

# Check cron jobs
psql $DIRECT_URL -c "SELECT jobname, schedule, active FROM cron.job;"

# Check extensions
psql $DIRECT_URL -c "SELECT extname FROM pg_extension WHERE extname IN ('pg_cron', 'pg_net');"
```

## Benefits

### For Developers
- ✅ Single command database setup
- ✅ Complete TypeScript type safety
- ✅ Clear separation of concerns
- ✅ Reproducible development environment

### For Operations
- ✅ Consistent deployment procedures
- ✅ No schema drift between environments
- ✅ Clear troubleshooting procedures
- ✅ Simplified maintenance and updates

## Migration from Old System

If you have an existing development environment:

1. **Backup important data** (if any)
2. **Run complete rebuild**: `npm run db:rebuild`
3. **Verify functionality**: Check that all features work
4. **Update any custom scripts** to use new workflow

The new system is backward compatible and will automatically set up the correct database structure.
