<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zeeguros Database ERD</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-style: italic;
        }
        .legend {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .legend h3 {
            margin-top: 0;
            color: #495057;
        }
        .legend-item {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 5px;
        }
        .legend-color {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 5px;
            vertical-align: middle;
            border-radius: 3px;
        }
        .user-auth { background-color: #e3f2fd; }
        .policy-insurance { background-color: #f3e5f5; }
        .auction-bidding { background-color: #e8f5e8; }
        .assets-docs { background-color: #fff3e0; }
        .notifications { background-color: #fce4ec; }
        #mermaid-diagram {
            text-align: center;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Zeeguros Insurance Platform - Database ERD</h1>
        <p class="subtitle">Complete Entity Relationship Diagram with updated BidCoverage model structure</p>
        
        <div class="legend">
            <h3>Entity Categories</h3>
            <div class="legend-item">
                <span class="legend-color user-auth"></span>
                User & Authentication
            </div>
            <div class="legend-item">
                <span class="legend-color policy-insurance"></span>
                Policy & Insurance
            </div>
            <div class="legend-item">
                <span class="legend-color auction-bidding"></span>
                Auction & Bidding
            </div>
            <div class="legend-item">
                <span class="legend-color assets-docs"></span>
                Assets & Documentation
            </div>
            <div class="legend-item">
                <span class="legend-color notifications"></span>
                Notifications
            </div>
        </div>

        <div id="mermaid-diagram">
            <div class="mermaid">
erDiagram
    %% User & Authentication Entities
    User {
        uuid id PK
        string email UK
        string phone UK
        string firstName
        string lastName
        string displayName
        Role role
        datetime createdAt
        datetime updatedAt
    }
    
    AccountHolderProfile {
        uuid id PK
        uuid userId FK
        datetime createdAt
        datetime updatedAt
    }
    
    BrokerProfile {
        uuid id PK
        uuid userId FK
        string registrationClass
        string registrationKey UK
        datetime registrationDate
        string legalName
        string identifier UK
        InsurerCompany insurerCompany
        boolean isAuthorizedByOther
        boolean isComplementary
        boolean isGroupAgent
        KYCStatus kycStatus
        string stripeCustomerId UK
        datetime createdAt
        datetime updatedAt
    }
    
    AdminProfile {
        uuid id PK
        uuid userId FK
        datetime createdAt
        datetime updatedAt
    }
    
    Address {
        uuid id PK
        uuid brokerId FK
        uuid insuredPartyId FK
        string street
        string city
        Province province
        Region region
        Country country
        string postalCode
    }
    
    %% Policy & Insurance Entities
    Policy {
        uuid id PK
        string policyNumber UK
        uuid accountHolderId FK
        PolicyType type
        PolicyStatus status
        decimal premium
        PaymentPeriod paymentPeriod
        date startDate
        date endDate
        datetime createdAt
        datetime updatedAt
    }
    
    Coverage {
        uuid id PK
        uuid policyId FK
        GuaranteeType type
        string customName
        decimal limit
        decimal deductible
        string description
    }
    
    InsuredParty {
        uuid id PK
        uuid accountHolderId FK
        uuid policyId FK
        PartyRole role
        string firstName
        string lastName
        string idNumber UK
        date birthDate
        Gender gender
        string licenseNumber
        date licenseIssueDate
        datetime createdAt
        datetime updatedAt
    }
    
    %% Assets & Documentation
    Asset {
        uuid id PK
        uuid accountHolderId FK
        uuid policyId FK
        AssetType type
        string brand
        string model
        string licensePlate UK
        string vin UK
        int year
        FuelType fuelType
        GarageType garageType
        UsageType usageType
        KmRange kmRange
        decimal marketValue
        datetime createdAt
        datetime updatedAt
    }
    
    Documentation {
        uuid id PK
        uuid accountHolderId FK
        uuid brokerId FK
        uuid bidId FK
        DocumentType type
        string fileName
        string filePath
        int fileSize
        string mimeType
        datetime uploadedAt
    }
    
    %% Auction & Bidding Entities
    Auction {
        uuid id PK
        string auctionNumber UK
        uuid accountHolderId FK
        uuid policyId FK
        AuctionState state
        datetime startDate
        datetime endDate
        decimal reservePrice
        datetime createdAt
        datetime updatedAt
    }
    
    Bid {
        uuid id PK
        uuid auctionId FK
        uuid brokerId FK
        decimal amount
        uuid documentId FK
        datetime createdAt
        datetime updatedAt
    }
    
    BidCoverage {
        uuid id PK
        uuid bidId FK
        GuaranteeType type
        string customName
        decimal limit
        decimal deductible
        string description
    }
    
    AuctionWinner {
        uuid id PK
        uuid auctionId FK
        uuid brokerId FK
        uuid bidId FK
        decimal winningAmount
        datetime selectedAt
        boolean contactDataRevealed
        datetime contactRevealedAt
    }
    
    AuctionCommission {
        uuid id PK
        uuid auctionId FK
        uuid brokerId FK
        decimal amount
        string stripePaymentIntentId UK
        string status
        datetime paidAt
        datetime createdAt
    }
    
    %% Subscriptions & Billing
    Subscription {
        uuid id PK
        uuid brokerId FK
        string stripeSubscriptionId UK
        string status
        datetime currentPeriodEnd
        datetime createdAt
        datetime updatedAt
    }
    
    %% Notifications
    Notification {
        uuid id PK
        NotificationType type
        RecipientType recipientType
        uuid recipientId
        string title
        string message
        json data
        boolean isRead
        datetime createdAt
    }
    
    %% Relationships
    User ||--o| AccountHolderProfile : "has"
    User ||--o| BrokerProfile : "has"
    User ||--o| AdminProfile : "has"
    
    BrokerProfile ||--o| Address : "billing address"
    InsuredParty ||--o| Address : "residence"
    
    AccountHolderProfile ||--o{ Policy : "owns"
    AccountHolderProfile ||--o{ Asset : "owns"
    AccountHolderProfile ||--o{ InsuredParty : "insures"
    AccountHolderProfile ||--o{ Auction : "creates"
    AccountHolderProfile ||--o{ Documentation : "uploads"
    
    Policy ||--o{ Coverage : "includes"
    Policy ||--o{ InsuredParty : "covers"
    Policy ||--o{ Asset : "insures"
    Policy ||--o| Auction : "auctioned"
    
    Auction ||--o{ Bid : "receives"
    Auction ||--o| AuctionWinner : "has winner"
    Auction ||--o{ AuctionCommission : "generates"
    
    BrokerProfile ||--o{ Bid : "places"
    BrokerProfile ||--o{ Subscription : "subscribes"
    BrokerProfile ||--o{ AuctionWinner : "wins"
    BrokerProfile ||--o{ AuctionCommission : "pays"
    BrokerProfile ||--o{ Documentation : "uploads"
    
    Bid ||--o{ BidCoverage : "includes"
    Bid ||--o| Documentation : "includes"
    
    Bid ||--o| AuctionWinner : "selected as winner"
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            er: {
                entityPadding: 15,
                fontSize: 12,
                useMaxWidth: true
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>