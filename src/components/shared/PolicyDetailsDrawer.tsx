"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er<PERSON>onte<PERSON> } from "@/components/shared/drawer";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { CoverageCard } from "@/features/auctions/components/coverage-card";
import {
  groupCoveragesByGroup,
  getCoverageGroupsInOrder,
  formatCoverageLimit,
  formatCoverageDeductible,
  getCoverageGroup
} from "@/features/account-holder/utils/coverage-normalization";
import { translateCoverageGroup } from "@/features/policies/utils/translations";
import { useSendOffer } from "@/features/auctions/hooks/use-send-offer";
import { usePolicyInsuredParties } from "@/features/policies/hooks/usePolicyInsuredParties";
import { cn } from "@/lib/utils";
import { maskPolicyNumber, maskName, maskLicensePlate, formatCurrency } from "@/lib/utils";

import { Upload, FileText } from "lucide-react";
import { CheckCircle } from "lucide-react";
import { PolicyDetailsDrawerProps } from "@/types/policy";
import { translatePartyRole, translateGender, translateFuelType, translateUsageType, translateGarageType, translateKmRange } from "@/features/policies/utils/translations";
import { PartyRole, Gender } from "@prisma/client";
import { comparePolicyWithBid, PolicyBidComparison } from "@/features/account-holder/utils/policy-comparison";
import { PolicyCoverage } from "@/types/policy";

// Helper function to get detailed coverage information for comparison
function getCoverageDetails(coverage: any) {
  const details = {
    limit: formatCoverageLimit(coverage),
    deductible: formatCoverageDeductible(coverage),
    hasLimitInfo: !!(coverage.limit || coverage.limitIsUnlimited || coverage.limitIsFullCost ||
                     coverage.limitPerDay || coverage.liabilityBodilyCap || coverage.liabilityPropertyCap),
    hasDeductibleInfo: !!(coverage.deductible || coverage.deductiblePercent),
    specialFlags: [] as string[]
  };

  // Add special flags for display
  if (coverage.limitIsUnlimited) {
    details.specialFlags.push("Ilimitado");
  }
  if (coverage.limitIsFullCost) {
    details.specialFlags.push("Coste completo");
  }
  if (coverage.limitPerDay) {
    details.specialFlags.push("Límite diario");
  }
  if (coverage.liabilityBodilyCap || coverage.liabilityPropertyCap) {
    details.specialFlags.push("RC específica");
  }

  return details;
}

// Helper component for grouped coverage display
export function GroupedCoverageDisplay({ coverages }: { coverages: PolicyCoverage[] }) {
  // Helper function to safely convert decimal values to numbers
  const safeDecimalToNumber = (value: number | null | undefined): number | null => {
    if (value === null || value === undefined) return null;
    return Number(value);
  };

  // Convert PolicyCoverage to Coverage-like structure for grouping
  const convertedCoverages = coverages.map(coverage => ({
    type: coverage.guaranteeType,
    customName: coverage.customName || null,
    description: coverage.description,
    limit: safeDecimalToNumber(coverage.limit),
    limitIsUnlimited: coverage.limitIsUnlimited || false,
    limitIsFullCost: coverage.limitIsFullCost || false,
    limitPerDay: safeDecimalToNumber(coverage.limitPerDay),
    limitMaxDays: coverage.limitMaxDays || null,
    limitMaxMonths: coverage.limitMaxMonths || null,
    liabilityBodilyCap: safeDecimalToNumber(coverage.liabilityBodilyCap),
    liabilityPropertyCap: safeDecimalToNumber(coverage.liabilityPropertyCap),
    deductible: safeDecimalToNumber(coverage.deductible),
    deductiblePercent: safeDecimalToNumber(coverage.deductiblePercent)
  }));

  console.log('🔍 Converted coverages:', convertedCoverages);

  const groupedCoverages = groupCoveragesByGroup(convertedCoverages);
  console.log('🔍 Grouped coverages:', groupedCoverages);

  const activeGroups = getCoverageGroupsInOrder().filter(group =>
    groupedCoverages[group] && groupedCoverages[group].length > 0
  );

  console.log('🔍 Active groups:', activeGroups);

  return (
    <div>
      <div className="text-sm sm:text-base text-gray-600 mb-4">
        Total: {coverages.length} coberturas en {activeGroups.length} categorías
      </div>

      <Accordion type="multiple" defaultValue={activeGroups.slice(0, 3)} className="w-full">
        {activeGroups.map((group) => (
          <AccordionItem key={group} value={group} className="border-b">
            <AccordionTrigger className="text-sm font-medium no-underline hover:no-underline px-2 py-3">
              <div className="flex items-center justify-between w-full mr-4">
                <span>{translateCoverageGroup(group)}</span>
                <span className="text-xs text-gray-500">
                  {groupedCoverages[group].length} cobertura{groupedCoverages[group].length !== 1 ? 's' : ''}
                </span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-2 pb-4">
              <div className="space-y-3">
                {groupedCoverages[group].map((coverage, index) => (
                  <CoverageCard
                    key={`${coverage.type}-${index}`}
                    type={coverage.type}
                    customName={coverage.customName}
                    description={coverage.description}
                    limit={coverage.limit ? Number(coverage.limit) : null}
                    limitIsUnlimited={coverage.limitIsUnlimited}
                    limitIsFullCost={coverage.limitIsFullCost}
                    limitPerDay={coverage.limitPerDay ? Number(coverage.limitPerDay) : null}
                    limitMaxDays={coverage.limitMaxDays}
                    limitMaxMonths={coverage.limitMaxMonths}
                    liabilityBodilyCap={coverage.liabilityBodilyCap ? Number(coverage.liabilityBodilyCap) : null}
                    liabilityPropertyCap={coverage.liabilityPropertyCap ? Number(coverage.liabilityPropertyCap) : null}
                    deductible={coverage.deductible ? Number(coverage.deductible) : null}
                    deductiblePercent={coverage.deductiblePercent ? Number(coverage.deductiblePercent) : null}
                  />
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}

export function PolicyDetailsDrawer({
  isOpen,
  onClose,
  mode = "broker", // Default to broker mode for backward compatibility
  policyData,
  comparisonData,
}: PolicyDetailsDrawerProps) {
  const [annualPremium, setAnnualPremium] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const drawerRef = useRef<HTMLDivElement>(null);
  const firstFocusableRef = useRef<HTMLButtonElement>(null);

  const sendOfferMutation = useSendOffer();

  // Keyboard navigation and focus management
  useEffect(() => {
    if (isOpen) {
      // Focus the first focusable element when drawer opens
      setTimeout(() => {
        firstFocusableRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      // Close drawer on Escape key
      if (event.key === 'Escape') {
        event.preventDefault();
        onClose();
        return;
      }

      // Tab navigation within drawer
      if (event.key === 'Tab') {
        const drawer = drawerRef.current;
        if (!drawer) return;

        const focusableElements = drawer.querySelectorAll(
          'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"]):not([disabled])'
        );
        
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        if (event.shiftKey) {
          // Shift + Tab (backward)
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement?.focus();
          }
        } else {
          // Tab (forward)
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement?.focus();
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  // Comparison logic
  const isComparisonMode = mode === "comparison" && comparisonData?.showComparison;
  const comparison: PolicyBidComparison | null = isComparisonMode && policyData && comparisonData?.bid
    ? comparePolicyWithBid(policyData, comparisonData.bid)
    : null;



  // Fetch insured parties data when drawer is open and policy ID is available
  const { data: insuredParties, isLoading: isLoadingInsuredParties } = usePolicyInsuredParties(
    isOpen && policyData?.id ? policyData.id : null
  );

  const handleFileSelect = (file: File) => {
    // Validate file type and size
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      alert("Solo se permiten archivos PDF, DOC y DOCX");
      return;
    }

    if (file.size > maxSize) {
      alert("El archivo no puede superar los 10MB");
      return;
    }

    setSelectedFile(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0 && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0 && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleSendOffer = () => {
    if (!annualPremium || !policyData) {
      alert("Por favor, introduce la prima anual");
      return;
    }

    setShowConfirmDialog(true);
  };

  const handleConfirmSend = async () => {
    if (!policyData) return;

    try {
      await sendOfferMutation.mutateAsync({
        policyId: policyData.id,
        annualPremium: parseFloat(annualPremium),
        // TODO: Upload file and get URL
        fileUrl: selectedFile ? "https://example.com/file.pdf" : undefined,
      });

      setShowConfirmDialog(false);
      onClose();
      setAnnualPremium("");
      setSelectedFile(null);
    } catch (error) {
      console.error("Error sending offer:", error);
      alert("Error al enviar la oferta. Por favor, inténtalo de nuevo.");
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "border-transparent bg-primary text-primary-foreground hover:bg-primary/80";
      case "EXPIRED":
        return "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80";
      case "DISPONIBLE":
        return "bg-[#3AE386] text-black";
      case "PARTICIPANDO":
        return "bg-[#3AE386] text-black";
      case "GANADA":
        return "bg-[#3AE386] text-black";
      case "CONFIRMADA":
        return "bg-[#3AE386] text-black";
      case "PERDIDAS":
      case "CANCELED":
      case "CLOSED":
        return "bg-gray-500 text-black";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getStatusDisplayText = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "ACTIVA";
      case "CONFIRMADA":
        return "CONFIRMADA";
      case "PERDIDAS":
        return "PERDIDA";
      case "EXPIRED":
        return "EXPIRADA";
      case "CANCELED":
        return "CANCELADA";
      case "CLOSED":
        return "CERRADA";
      default:
        return status;
    }
  };

  // Determine if data should be masked based on mode and status
  const shouldMaskData = mode === "broker" && !(policyData?.status === "CONFIRMADA");

  // Determine if broker actions should be shown
  const showBrokerActions = mode === "broker";
  const offerDisabledStatuses = ["CONFIRMADA", "FINALIZADA", "PERDIDAS", "EXPIRED", "CANCELED", "CLOSED"];

  if (!policyData) return null;

  return (
    <>
      <Drawer isOpen={isOpen} onClose={onClose}>
        <DrawerHeader onClose={onClose}>
          <button
            ref={firstFocusableRef}
            className="sr-only"
            onFocus={(e) => {
              // Move focus to the close button when this hidden button is focused
              const closeButton = e.currentTarget.parentElement?.querySelector('[aria-label="Cerrar"]') as HTMLElement;
              closeButton?.focus();
            }}
          >
            Primer elemento enfocable
          </button>
          <div className="flex items-center gap-3">
            <h4 id="drawer-title" className="text-xl font-semibold text-gray-900">
              {isComparisonMode 
                ? "Comparación de Ofertas" 
                : mode === "account-holder" 
                  ? "Detalles de la Póliza" 
                  : "Detalles de la Subasta"
              }
            </h4>
            {!isComparisonMode && (
              <Badge
                className={cn(
                  "text-sm font-medium",
                  getStatusBadgeColor(policyData.status)
                )}
              >
                {getStatusDisplayText(policyData.status)}
              </Badge>
            )}
          </div>
        </DrawerHeader>

        <DrawerContent className="px-0 pb-6">
          <div 
            ref={drawerRef} 
            role="dialog" 
            aria-modal="true" 
            aria-labelledby="drawer-title"
            className="h-full"
          >
            <div id="drawer-content" className="sr-only">
              Contenido del drawer con información de póliza y opciones de comparación
            </div>
            <Accordion type="multiple" defaultValue={["info", "documents"]} className="w-full" role="tablist">
              {/* Información de Póliza */}
              <AccordionItem value="info" className="border-b">
                <AccordionTrigger className="text-base font-semibold no-underline hover:no-underline px-4 sm:px-6 pt-0 pb-4">
                  📑 {isComparisonMode ? "Resumen" : "Información de Póliza"}
                </AccordionTrigger>
                <AccordionContent className="space-y-3 px-4 sm:px-6">
                  {isComparisonMode && comparison ? (
                    <div className="space-y-4">
                      {/* Summary Cards */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Current Policy Card */}
                        <div className="border border-gray-200 rounded-lg p-4 bg-white">
                          <h4 className="font-medium text-gray-900 mb-3">Póliza Actual</h4>
                          <div className="space-y-2">
                            <div>
                              <span className="text-gray-600 text-sm">Aseguradora:</span>
                              <div className="font-medium text-gray-900">{policyData.insurer}</div>
                            </div>
                            <div>
                              <span className="text-gray-600 text-sm">Prima anual:</span>
                              <div className="font-medium text-gray-900 flex items-center gap-2">
                                {typeof policyData.annualPremium === 'number' ? formatCurrency(policyData.annualPremium) : policyData.annualPremium}
                                {comparison.summary.premiumComparison.difference?.type === 'worse' && comparison.summary.premiumComparison.difference?.amount && (
                                  <span className="text-red-500 text-sm">↑ {formatCurrency(comparison.summary.premiumComparison.difference.amount)}</span>
                                )}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600 text-sm">Coberturas:</span>
                              <div className="font-medium text-gray-900 flex items-center gap-2">
                                {policyData.coverages.length}
                                {comparison.coverageComparison.coverages && 
                                 policyData.coverages.length < comparison.coverageComparison.coverages.filter(c => c.present).length && (
                                  <span className="text-red-500 text-sm">↓ {comparison.coverageComparison.coverages.filter(c => c.present).length - policyData.coverages.length}</span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Received Offer Card */}
                        <div className="border-2 border-[#3AE386] rounded-lg p-4 bg-white">
                          <h4 className="font-medium text-gray-900 mb-3">Oferta Recibida</h4>
                          <div className="space-y-2">
                            <div>
                              <span className="text-gray-600 text-sm">Aseguradora:</span>
                              <div className="font-medium text-gray-900">{comparisonData!.bid.brokerCompany}</div>
                            </div>
                            <div>
                               <span className="text-gray-600 text-sm">Prima anual:</span>
                               <div className="font-medium text-gray-900 flex items-center gap-2">
                                 {formatCurrency(comparisonData!.bid.annualPremium)}
                                 {comparison.summary.premiumComparison.difference?.type === 'better' && comparison.summary.premiumComparison.difference?.amount && (
                                   <span className="text-[#3AE386] text-sm font-medium">
                                     ↓ {formatCurrency(comparison.summary.premiumComparison.difference.amount)}
                                   </span>
                                 )}
                               </div>
                             </div>
                             <div>
                               <span className="text-gray-600 text-sm">Coberturas:</span>
                               <div className="font-medium text-gray-900 flex items-center gap-2">
                                 {comparison.coverageComparison.coverages ? comparison.coverageComparison.coverages.filter(c => c.present).length : 'N/A'}
                                 {comparison.coverageComparison.coverages && 
                                  comparison.coverageComparison.coverages.filter(c => c.present).length > policyData.coverages.length && (
                                   <span className="text-[#3AE386] text-sm">↑ {comparison.coverageComparison.coverages.filter(c => c.present).length - policyData.coverages.length}</span>
                                 )}
                                 {comparison.coverageComparison.coverages && 
                                  comparison.coverageComparison.coverages.filter(c => c.present).length < policyData.coverages.length && (
                                   <span className="text-red-500 text-sm">↓ {policyData.coverages.length - comparison.coverageComparison.coverages.filter(c => c.present).length}</span>
                                 )}
                               </div>
                             </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm sm:text-base">
                      <div>
                        <span className="text-gray-600">Número de Póliza:</span>
                        <div className="font-medium break-all">
                          {shouldMaskData ? maskPolicyNumber(policyData.policyNumber) : policyData.policyNumber}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600">Aseguradora:</span>
                        <div className="font-medium">{policyData.insurer}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Producto:</span>
                        <div className="font-medium">{policyData.product}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Tipo de Póliza:</span>
                        <div className="font-medium">{policyData.policyType}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Vigencia:</span>
                        <div className="font-medium">{policyData.validity}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Prima Anual:</span>
                        <div className="font-medium">{policyData.annualPremium}</div>
                      </div>
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>

              {/* Partes Aseguradas - Datos protegidos solo en modo broker */}
              {!isComparisonMode && (
                <AccordionItem value="insured-parties" className="border-b">
                <AccordionTrigger className="text-base font-semibold no-underline hover:no-underline px-4 sm:px-6">
                  <div className="flex items-center gap-2">
                    <span>👥 Partes Aseguradas</span>
                    {shouldMaskData && (
                      <Badge className="bg-red-500 text-white text-xs hover:bg-red-500 hover:text-white">
                        Datos protegidos
                      </Badge>
                    )}
                  </div>
                </AccordionTrigger>
                <AccordionContent className="space-y-3 px-4 sm:px-6">
                  {isLoadingInsuredParties ? (
                    <div className="text-sm sm:text-base text-gray-600">
                      Cargando partes aseguradas...
                    </div>
                  ) : insuredParties && insuredParties.length > 0 ? (
                    <div className="space-y-4">
                      <div className="text-sm sm:text-base text-gray-600 mb-4">
                        Total: {insuredParties.length} parte{insuredParties.length !== 1 ? 's' : ''} asegurada{insuredParties.length !== 1 ? 's' : ''}
                      </div>
                      {insuredParties.map((party) => (
                        <div key={party.id} className="border rounded-lg p-4 bg-gray-50">
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm sm:text-base">
                            <div>
                              <span className="text-gray-600">Nombre:</span>
                              <div className="font-medium">
                                {shouldMaskData ? maskName(party.fullName) : party.fullName}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Identificación:</span>
                              <div className="font-medium">
                                {shouldMaskData ? '*********' : party.identification}
                              </div>
                            </div>
                            <div>
                              <span className="text-gray-600">Roles:</span>
                              <div className="font-medium">
                                {party.roles.map(role => translatePartyRole(role as PartyRole)).join(', ')}
                              </div>
                            </div>
                            {party.gender && (
                              <div>
                                <span className="text-gray-600">Género:</span>
                                <div className="font-medium">{translateGender(party.gender as Gender)}</div>
                              </div>
                            )}
                            {party.birthDate && (
                              <div>
                                <span className="text-gray-600">Fecha de Nacimiento:</span>
                                <div className="font-medium">
                                  {new Date(party.birthDate).toLocaleDateString('es-ES')}
                                </div>
                              </div>
                            )}
                            {party.driverLicenseNumber && (
                              <div>
                                <span className="text-gray-600">Licencia de Conducir:</span>
                                <div className="font-medium">
                                  {shouldMaskData ? '*********' : party.driverLicenseNumber}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm sm:text-base text-gray-600">
                      No hay partes aseguradas registradas para esta póliza.
                    </div>
                  )}
                </AccordionContent>
                </AccordionItem>
              )}

              {/* Vehículo */}
              {!isComparisonMode && (
                <AccordionItem value="vehicle" className="border-b">
                <AccordionTrigger className="text-base font-semibold no-underline hover:no-underline px-4 sm:px-6">
                  🚗 Vehículo
                </AccordionTrigger>
                <AccordionContent className="space-y-3 px-4 sm:px-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm sm:text-base">
                    <div>
                      <span className="text-gray-600">Matrícula:</span>
                      <div className="font-medium">
                        {shouldMaskData ? maskLicensePlate(policyData.vehiclePlate) : policyData.vehiclePlate}
                      </div>
                    </div>
                    {policyData.vehicleFirstRegistrationDate && (
                      <div>
                        <span className="text-gray-600">Fecha de primera matriculación:</span>
                        <div className="font-medium">
                          {policyData.vehicleFirstRegistrationDate}
                        </div>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-600">Marca:</span>
                      <div className="font-medium">{policyData.vehicleBrand}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Modelo:</span>
                      <div className="font-medium">{policyData.vehicleModel}</div>
                    </div>
                    {policyData.vehicleVersion && (
                      <div>
                        <span className="text-gray-600">Versión:</span>
                        <div className="font-medium">{policyData.vehicleVersion}</div>
                      </div>
                    )}
                    {policyData.vehicleManufacturingYear && (
                      <div>
                        <span className="text-gray-600">Año de fabricación:</span>
                        <div className="font-medium">{policyData.vehicleManufacturingYear}</div>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-600">Tipo de Vehículo:</span>
                      <div className="font-medium">{policyData.vehicleType}</div>
                    </div>
                    {policyData.vehicleFuelType && (
                      <div>
                        <span className="text-gray-600">Combustible:</span>
                        <div className="font-medium">{translateFuelType(policyData.vehicleFuelType)}</div>
                      </div>
                    )}
                    {policyData.vehicleVin && (
                      <div>
                        <span className="text-gray-600">Bastidor/VIN:</span>
                        <div className="font-medium">
                          {shouldMaskData ? '*********' : policyData.vehicleVin}
                        </div>
                      </div>
                    )}
                    {policyData.vehiclePower && (
                      <div>
                        <span className="text-gray-600">Potencia:</span>
                        <div className="font-medium">{policyData.vehiclePower} CV</div>
                      </div>
                    )}
                    {policyData.vehicleSeats && (
                      <div>
                        <span className="text-gray-600">Plazas:</span>
                        <div className="font-medium">{policyData.vehicleSeats}</div>
                      </div>
                    )}
                    {policyData.vehicleUsageType && (
                      <div>
                        <span className="text-gray-600">Tipo de Uso:</span>
                        <div className="font-medium">{translateUsageType(policyData.vehicleUsageType)}</div>
                      </div>
                    )}
                    {policyData.vehicleGarageType && (
                      <div>
                        <span className="text-gray-600">Tipo de Garaje:</span>
                        <div className="font-medium">{translateGarageType(policyData.vehicleGarageType)}</div>
                      </div>
                    )}
                    {policyData.vehicleKmPerYear && (
                      <div>
                        <span className="text-gray-600">KM/Año:</span>
                        <div className="font-medium">{translateKmRange(policyData.vehicleKmPerYear)}</div>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-600">Vehículo Leasing:</span>
                      <div className="font-medium">{policyData.vehicleIsLeased}</div>
                    </div>
                  </div>
                </AccordionContent>
                </AccordionItem>
              )}

              {/* Coberturas */}
              <AccordionItem value="coverages" className="border-b">
                <AccordionTrigger className="text-base font-semibold no-underline hover:no-underline px-4 sm:px-6">
                  🛡️ {isComparisonMode ? "Comparación de Coberturas" : "Coberturas"}
                </AccordionTrigger>
                <AccordionContent className="space-y-3 px-4 sm:px-6">
                  {isComparisonMode && comparison ? (
                    <div className="space-y-4">
                      {/* Coverage Analysis Summary */}
                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-3 h-3 rounded-full bg-[#3ea050]" />
                          <h3 className="font-semibold text-gray-900">Análisis de Coberturas</h3>
                        </div>
                        <p className="text-sm text-gray-600">
                          {comparison.coverageComparison.coverages?.length || 0} coberturas analizadas en {getCoverageGroupsInOrder().filter(group => {
                            const groupCoverages = groupCoveragesByGroup(policyData.coverages.map(coverage => ({
                              type: coverage.guaranteeType,
                              customName: coverage.customName || null,
                              description: coverage.description,
                              limit: coverage.limit ? Number(coverage.limit) : null,
                              limitIsUnlimited: coverage.limitIsUnlimited || false,
                              limitIsFullCost: coverage.limitIsFullCost || false,
                              limitPerDay: coverage.limitPerDay ? Number(coverage.limitPerDay) : null,
                              limitMaxDays: coverage.limitMaxDays || null,
                              limitMaxMonths: coverage.limitMaxMonths || null,
                              liabilityBodilyCap: coverage.liabilityBodilyCap ? Number(coverage.liabilityBodilyCap) : null,
                              liabilityPropertyCap: coverage.liabilityPropertyCap ? Number(coverage.liabilityPropertyCap) : null,
                              deductible: coverage.deductible ? Number(coverage.deductible) : null,
                              deductiblePercent: coverage.deductiblePercent ? Number(coverage.deductiblePercent) : null
                            })));
                            return groupCoverages[group] && groupCoverages[group].length > 0;
                          }).length} categorías
                        </p>
                      </div>

                      {/* Grouped coverage comparisons */}
                      {comparison.coverageComparison.coverages &&
                       comparison.coverageComparison.coverages.length > 0 && (
                        <Accordion type="multiple" defaultValue={getCoverageGroupsInOrder().slice(0, 3)} className="w-full">
                          {getCoverageGroupsInOrder().map((group) => {
                            // Filter coverages for this group
                            const groupCoverages = comparison.coverageComparison.coverages?.filter(coverage => {
                              // Convert PolicyCoverage to Coverage-like structure for grouping
                              const convertedCoverage = {
                                type: coverage.guaranteeType,
                                customName: null,
                                description: null,
                                limit: null,
                                limitIsUnlimited: false,
                                limitIsFullCost: false,
                                limitPerDay: null,
                                limitMaxDays: null,
                                limitMaxMonths: null,
                                liabilityBodilyCap: null,
                                liabilityPropertyCap: null,
                                deductible: null,
                                deductiblePercent: null
                              };
                              const coverageGroup = getCoverageGroup(convertedCoverage.type);
                              return coverageGroup === group;
                            }) || [];

                            if (groupCoverages.length === 0) return null;

                            return (
                              <AccordionItem key={group} value={group} className="border-b">
                                <AccordionTrigger className="text-sm font-medium no-underline hover:no-underline px-2 py-3">
                                  <div className="flex items-center justify-between w-full mr-4">
                                    <div className="flex items-center gap-2">
                                      <div className="w-2 h-2 rounded-full bg-[#3ea050]" />
                                      <span>{translateCoverageGroup(group)}</span>
                                    </div>
                                    <span className="text-xs text-gray-500">
                                      {groupCoverages.length} cobertura{groupCoverages.length !== 1 ? 's' : ''}
                                    </span>
                                  </div>
                                </AccordionTrigger>
                                <AccordionContent className="px-2 pb-4">
                                  <div className="space-y-4">
                                    {groupCoverages.map((coverage, index) => {
                            // Get policy coverage data for detailed comparison
                            const policyCoverage = policyData.coverages.find(pc => pc.guaranteeType === coverage.guaranteeType);
                            const bidCoverage = comparisonData?.bid?.bidCoverages?.find((bc: any) => bc.type === coverage.guaranteeType);

                            // Get detailed coverage information
                            const policyDetails = policyCoverage ? getCoverageDetails(policyCoverage) : null;
                            const bidDetails = bidCoverage ? getCoverageDetails(bidCoverage) : null;

                            // Determine comparison status
                            const getComparisonStatus = () => {
                              if (!bidCoverage) {
                                return {
                                  type: 'missing' as const,
                                  label: 'Cobertura no incluida',
                                  icon: '✗',
                                  description: 'Esta cobertura no está incluida en la nueva oferta'
                                };
                              }
                              if (!policyCoverage) {
                                return {
                                  type: 'better' as const,
                                  label: 'Nueva cobertura',
                                  icon: '+',
                                  description: 'Nueva cobertura añadida en la oferta'
                                };
                              }

                              // Compare limits for better/worse determination
                              if (bidCoverage.limitIsUnlimited && !policyCoverage.limitIsUnlimited) {
                                return {
                                  type: 'better' as const,
                                  label: 'Límite mejorado',
                                  icon: '↗',
                                  description: 'El límite de cobertura ha sido mejorado a ilimitado'
                                };
                              }
                              if (!bidCoverage.limitIsUnlimited && policyCoverage.limitIsUnlimited) {
                                return {
                                  type: 'worse' as const,
                                  label: 'Límite reducido',
                                  icon: '↘',
                                  description: 'El límite de cobertura ha sido reducido desde ilimitado'
                                };
                              }
                              if (bidCoverage.limit && policyCoverage.limit) {
                                const bidLimit = Number(bidCoverage.limit);
                                const policyLimit = Number(policyCoverage.limit);
                                if (bidLimit > policyLimit) {
                                  return {
                                    type: 'better' as const,
                                    label: 'Límite mejorado',
                                    icon: '↗',
                                    description: `Límite aumentado de ${formatCurrency(policyLimit)} a ${formatCurrency(bidLimit)}`
                                  };
                                }
                                if (bidLimit < policyLimit) {
                                  return {
                                    type: 'worse' as const,
                                    label: 'Límite reducido',
                                    icon: '↘',
                                    description: `Límite reducido de ${formatCurrency(policyLimit)} a ${formatCurrency(bidLimit)}`
                                  };
                                }
                              }

                              // Check deductible changes
                              if (bidCoverage.deductible && policyCoverage.deductible) {
                                const bidDeductible = Number(bidCoverage.deductible);
                                const policyDeductible = Number(policyCoverage.deductible);
                                if (bidDeductible < policyDeductible) {
                                  return {
                                    type: 'better' as const,
                                    label: 'Franquicia mejorada',
                                    icon: '↗',
                                    description: `Franquicia reducida de ${formatCurrency(policyDeductible)} a ${formatCurrency(bidDeductible)}`
                                  };
                                }
                                if (bidDeductible > policyDeductible) {
                                  return {
                                    type: 'worse' as const,
                                    label: 'Franquicia aumentada',
                                    icon: '↘',
                                    description: `Franquicia aumentada de ${formatCurrency(policyDeductible)} a ${formatCurrency(bidDeductible)}`
                                  };
                                }
                              }

                              return {
                                type: 'same' as const,
                                label: 'Sin cambios',
                                icon: '=',
                                description: 'No hay cambios significativos en esta cobertura'
                              };
                            };

                            const comparisonStatus = getComparisonStatus();

                            return (
                              <div key={index} className="space-y-3">
                                {/* Coverage Title */}
                                <div className="flex items-center gap-2">
                                  <div className={cn(
                                    "w-2 h-2 rounded-full",
                                    comparisonStatus.type === 'better' ? 'bg-green-500' :
                                    comparisonStatus.type === 'worse' || comparisonStatus.type === 'missing' ? 'bg-red-500' :
                                    'bg-gray-500'
                                  )} />
                                  <h4 className="font-medium text-gray-900">
                                    {coverage.guaranteeType === 'OTHER' && (policyCoverage?.customName || bidCoverage?.customName)
                                      ? (policyCoverage?.customName || bidCoverage?.customName)
                                      : coverage.title
                                    }
                                  </h4>
                                </div>

                                {/* Comparison Cards */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  {/* Current Policy Card */}
                                  <div className="border-2 border-gray-300 rounded-lg p-4 bg-white shadow-sm">
                                    <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-100">
                                      <span className="text-sm font-bold text-gray-700 tracking-wide">PÓLIZA ACTUAL</span>
                                      <div className="w-3 h-3 rounded-full bg-gray-400 shadow-sm" />
                                    </div>

                                    {policyCoverage && policyDetails ? (
                                      <div className="space-y-2">
                                        <div>
                                          <span className="text-xs text-gray-500">Límite de cobertura:</span>
                                          <div className="font-medium text-sm text-gray-900">
                                            {policyDetails.limit}
                                          </div>
                                        </div>
                                        {policyDetails.hasDeductibleInfo && (
                                          <div>
                                            <span className="text-xs text-gray-500">Franquicia:</span>
                                            <div className="font-medium text-sm text-gray-900">
                                              {policyDetails.deductible}
                                            </div>
                                          </div>
                                        )}
                                        {policyCoverage.description && (
                                          <div>
                                            <span className="text-xs text-gray-500">Descripción:</span>
                                            <div className="text-sm text-gray-700">
                                              {policyCoverage.description}
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    ) : (
                                      <div className="text-sm text-gray-500 italic">
                                        No incluida en póliza actual
                                      </div>
                                    )}
                                  </div>

                                  {/* New Offer Card */}
                                  <div className={cn(
                                    "border-2 rounded-lg p-4 shadow-sm",
                                    comparisonStatus.type === 'better' ? 'bg-green-50 border-green-300' :
                                    comparisonStatus.type === 'worse' || comparisonStatus.type === 'missing' ? 'bg-red-50 border-red-300' :
                                    'bg-gray-50 border-gray-300'
                                  )}>
                                    <div className="flex items-center justify-between mb-3 pb-2 border-b border-opacity-30 border-gray-400">
                                      <span className="text-sm font-bold text-gray-700 tracking-wide">NUEVA OFERTA</span>
                                      <div className={cn(
                                        "w-3 h-3 rounded-full shadow-sm",
                                        comparisonStatus.type === 'better' ? 'bg-green-500' :
                                        comparisonStatus.type === 'worse' || comparisonStatus.type === 'missing' ? 'bg-red-500' :
                                        'bg-gray-500'
                                      )} />
                                    </div>

                                    {bidCoverage && bidDetails ? (
                                      <div className="space-y-2">
                                        <div>
                                          <span className="text-xs text-gray-500">Límite de cobertura:</span>
                                          <div className="font-medium text-sm text-gray-900">
                                            {bidDetails.limit}
                                          </div>
                                        </div>
                                        {bidDetails.hasDeductibleInfo && (
                                          <div>
                                            <span className="text-xs text-gray-500">Franquicia:</span>
                                            <div className="font-medium text-sm text-gray-900">
                                              {bidDetails.deductible}
                                            </div>
                                          </div>
                                        )}
                                        {bidCoverage.description && (
                                          <div>
                                            <span className="text-xs text-gray-500">Descripción:</span>
                                            <div className="text-sm text-gray-700">
                                              {bidCoverage.description}
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    ) : (
                                      <div className="flex flex-col items-center justify-center py-4">
                                        <div className="flex items-center gap-2 mb-2">
                                          <span className="text-red-600 text-2xl font-bold">✗</span>
                                          <span className="text-sm text-red-700 font-bold">Sin cobertura</span>
                                        </div>
                                        <span className="text-xs text-red-600 text-center opacity-80">
                                          Esta cobertura no está incluida en la nueva oferta
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                </div>

                                {/* Enhanced Status Indicator */}
                                <div className="flex justify-center mt-4">
                                  <div className={cn(
                                    "inline-flex items-center gap-3 px-4 py-2 rounded-lg text-sm font-semibold border-2 shadow-sm",
                                    comparisonStatus.type === 'better' ? 'bg-green-50 text-green-800 border-green-300' :
                                    comparisonStatus.type === 'worse' || comparisonStatus.type === 'missing' ? 'bg-red-50 text-red-800 border-red-300' :
                                    'bg-gray-50 text-gray-800 border-gray-300'
                                  )}>
                                    <span className={cn(
                                      "text-lg font-bold",
                                      comparisonStatus.type === 'better' ? 'text-green-600' :
                                      comparisonStatus.type === 'worse' || comparisonStatus.type === 'missing' ? 'text-red-600' :
                                      'text-gray-600'
                                    )}>
                                      {comparisonStatus.icon}
                                    </span>
                                    <div className="flex flex-col">
                                      <span className="font-semibold">{comparisonStatus.label}</span>
                                      {comparisonStatus.description && (
                                        <span className="text-xs opacity-80 mt-0.5">{comparisonStatus.description}</span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  );
                })}
              </Accordion>
            )}
          </div>
        ) : (
          <GroupedCoverageDisplay coverages={policyData.coverages} />
        )}
                </AccordionContent>
              </AccordionItem>

              {/* Documentos - Show in account-holder mode */}
              {mode === "account-holder" && policyData.document && (
                <AccordionItem value="documents" className="border-b">
                  <AccordionTrigger className="text-base font-semibold no-underline hover:no-underline px-4 sm:px-6">
                    💾 Documentos de la Póliza
                  </AccordionTrigger>
                  <AccordionContent className="space-y-3 px-4 sm:px-6">
                    <div className="text-sm sm:text-base text-gray-600 mb-4">
                      Descarga tu documento de póliza cuando lo necesites.
                    </div>
                    <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <FileText className="h-8 w-8 text-blue-500" />
                          <div>
                            <p className="font-medium text-gray-900">
                              {policyData.document.fileName || `Póliza ${policyData.policyNumber}`}
                            </p>
                            <p className="text-sm text-gray-500">
                              {policyData.document.fileSize
                                ? `${(policyData.document.fileSize / 1024 / 1024).toFixed(2)} MB`
                                : 'Tamaño no disponible'
                              } • Subido el {new Date(policyData.document.uploadedAt).toLocaleDateString('es-ES')}
                            </p>
                          </div>
                        </div>
                        <Button
                          onClick={() => {
                            // Create a temporary link to download the file
                            const link = document.createElement('a');
                            // Use the getR2PublicUrl utility to generate the correct public URL
                            const publicUrl = `/api/documents/download?key=${encodeURIComponent(policyData.document!.url)}`;
                            link.href = publicUrl;
                            link.download = policyData.document!.fileName || `Poliza_${policyData.policyNumber}.pdf`;
                            link.target = '_blank';
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                          className="bg-primary hover:bg-primary/90 text-white"
                          size="sm"
                        >
                          <FileText className="h-4 w-4 mr-2" />
                          Descargar
                        </Button>
                      </div>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              )}

              {/* Enviar Oferta - Only show in broker mode */}
              {showBrokerActions && !offerDisabledStatuses.includes(policyData.status) ? (
                <AccordionItem value="offer">
                  <AccordionTrigger className="text-base font-semibold no-underline hover:no-underline px-4 sm:px-6">
                    💰 Enviar Oferta
                  </AccordionTrigger>
                  <AccordionContent className="space-y-4 px-4 sm:px-6">
                    {policyData.status === "GANADA" ? (
                      <div className="space-y-4">
                        <div className="bg-green-50 border border-green-200 rounded-md p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="h-5 w-5 text-green-500" />
                            <p className="font-medium text-green-900">¡Has ganado esta subasta!</p>
                          </div>
                          <p className="text-sm text-gray-600">Paga la comisión para acceder a los datos completos del cliente.</p>
                        </div>
                        <Button 
                          className="w-full bg-green-500 hover:bg-green-600 text-white font-medium rounded-full"
                          // TODO: Implement payment logic
                        >
                          Pagar Comisión - €21.00
                        </Button>
                      </div>
                    ) : (
                      <>
                        <div>
                          <Label htmlFor="annual-premium" className="text-sm font-medium">
                            Prima Anual (€)
                          </Label>
                          <Input
                            id="annual-premium"
                            type="number"
                            step="0.01"
                            placeholder="Ej: 285.50"
                            value={annualPremium}
                            onChange={(e) => setAnnualPremium(e.target.value)}
                            className="mt-1"
                          />
                        </div>
                        
                        <div>
                          <Label className="text-sm font-medium">
                            Adjunta tu cotización
                          </Label>
                          <div
                            className={cn(
                              "mt-1 border-2 border-dashed rounded-lg p-4 sm:p-6 text-center transition-colors",
                              dragActive
                                ? "border-[#3AE386] bg-green-50"
                                : "border-gray-300 hover:border-gray-400"
                            )}
                            onDrop={handleDrop}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                          >
                            {selectedFile ? (
                              <div className="flex items-center justify-center gap-2 flex-wrap">
                                <FileText className="h-5 w-5 text-gray-500" />
                                <span className="text-sm text-gray-700 break-all">
                                  {selectedFile.name}
                                </span>
                                <button
                                  onClick={() => setSelectedFile(null)}
                                  className="text-red-500 hover:text-red-700 ml-2"
                                >
                                  ✕
                                </button>
                              </div>
                            ) : (
                              <>
                                <Upload className="h-6 w-6 sm:h-8 sm:w-8 text-gray-400 mx-auto mb-2" />
                                <p className="text-sm text-gray-600">
                                  Arrastra un archivo aquí o{" "}
                                  <label className="text-[#3AE386] hover:text-[#3EA050] cursor-pointer">
                                    selecciona uno
                                    <input
                                      type="file"
                                      className="hidden"
                                      accept=".pdf,.doc,.docx"
                                      onChange={handleFileInputChange}
                                    />
                                  </label>
                                </p>
                                <p className="text-xs text-gray-500 mt-1">
                                  PDF, DOC, DOCX (máx. 10MB)
                                </p>
                              </>
                            )}
                          </div>
                        </div>
                        
                        <Button
                          onClick={handleSendOffer}
                          disabled={!annualPremium || sendOfferMutation.isLoading}
                          className="w-full bg-[#3AE386] hover:bg-[#3EA050] text-black font-medium"
                        >
                          {sendOfferMutation.isLoading ? "Enviando..." : "Enviar Oferta"}
                        </Button>
                      </>
                    )}
                  </AccordionContent>
                </AccordionItem>
              ) : null}
            </Accordion>
          </div>
        </DrawerContent>
      </Drawer>

      {/* Confirmation Dialog - Only show in broker mode */}
      {showBrokerActions && (
        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmar envío de oferta</AlertDialogTitle>
              <AlertDialogDescription>
                ¿Estás seguro de que quieres enviar una oferta de {annualPremium}€ anuales para esta póliza?
                {selectedFile && (
                  <span className="block mt-2">
                    Se incluirá el archivo: {selectedFile.name}
                  </span>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmSend}
                className="bg-[#3AE386] hover:bg-[#3EA050] text-black"
              >
                Confirmar envío
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );
}