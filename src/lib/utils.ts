import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Combines clsx and tailwind-merge for conditional class names
 * @param inputs - Class values to combine
 * @returns Combined class string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Sanitizes a filename by removing invalid characters
 * @param filename - The filename to sanitize
 * @returns Sanitized filename
 */
export function sanitizeFilename(filename: string): string {
  const lastDotIndex = filename.lastIndexOf(".");
  const name =
    lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
  const extension = lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
  const sanitizedName = name
    .replace(/[^a-zA-Z0-9\-_]/g, "_")
    .replace(/_{2,}/g, "_")
    .replace(/^_+|_+$/g, "");
  return sanitizedName + extension;
}

// =============================================================================
// FORMATTING UTILITIES
// =============================================================================

/**
 * Format a date using Spanish locale
 */
export const formatDate = (date: Date | string | null | undefined) => {
  if (!date) {
    return 'Fecha no disponible';
  }
  
  // Convert string to Date if necessary
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Check if the date is valid
  if (isNaN(dateObj.getTime())) {
    return 'Fecha no disponible';
  }
  
  return dateObj.toLocaleDateString('es-ES', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

/**
 * Format currency in EUR using Spanish locale
 */
export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount);
};

/**
 * Format asset type for display
 */
export const getAssetTypeIcon = (assetType: string) => {
  switch (assetType) {
    case 'CAR':
      return '🚗';
    case 'MOTORCYCLE':
      return '🏍️';
    default:
      return '📋';
  }
};

/**
 * Get asset type display name
 */
export const getAssetTypeDisplayName = (assetType: string) => {
  switch (assetType) {
    case 'CAR':
      return 'Coche';
    case 'MOTORCYCLE':
      return 'Moto';
    default:
      return 'Activo';
  }
};

/**
 * Masks a policy number to show only the last 5 digits
 * @param policyNumber - The full policy number
 * @returns Masked policy number showing only last 5 digits
 */
export const maskPolicyNumber = (policyNumber: string) => {
  if (!policyNumber || policyNumber.length <= 5) {
    return policyNumber;
  }
  
  const lastFiveDigits = policyNumber.slice(-5);
  const maskedPart = '*'.repeat(policyNumber.length - 5);
  
  return maskedPart + lastFiveDigits;
};

/**
 * Masks a name to show only the first name followed by asterisks
 * @param fullName - The full name to mask
 * @returns Masked name showing only first name followed by asterisks (e.g., "Juan *****")
 */
export const maskName = (fullName: string) => {
  if (!fullName || fullName.trim() === '') {
    return '***';
  }
  
  const nameParts = fullName.trim().split(' ');
  const firstName = nameParts[0];
  
  // If only one name part, show first name + asterisks
  if (nameParts.length === 1) {
    return `${firstName} *****`;
  }
  
  // If multiple name parts, show first name + asterisks
  return `${firstName} *****`;
};

/**
 * Masks a license plate to show only asterisks
 * @param licensePlate - The license plate to mask
 * @returns Masked license plate showing only asterisks (e.g., "****")
 */
export const maskLicensePlate = (licensePlate: string) => {
  if (!licensePlate || licensePlate.trim() === '') {
    return '****';
  }
  
  // Always return just asterisks regardless of the original format
  return '****';
};

/**
 * Masks a bid ID to show only the last 6 characters with BID# prefix
 * Similar to auction identifier masking pattern
 * @param bidId - The full bid ID
 * @returns Masked bid ID in format BID#xxxxxx
 */
export function maskBidId(bidId: string): string {
  if (!bidId || bidId.length < 6) {
    return "BID#******";
  }
  return `BID#${bidId.slice(-6).toUpperCase()}`;
}

// =============================================================================
// NAVIGATION UTILITIES
// =============================================================================

/**
 * Valid user roles in the application
 */
export type UserRole = 'ACCOUNT_HOLDER' | 'BROKER' | 'ADMIN';

/**
 * Generates role-based navigation URLs for consistent routing across the platform
 * @param role - The user's role (ACCOUNT_HOLDER, BROKER, ADMIN)
 * @param domain - The business domain (policies, auctions, settings, support, dashboard, portfolio)
 * @returns The appropriate URL path for the role and domain
 */
export function getRoleBasedUrl(role: UserRole, domain: string): string {
  const rolePrefix = role === 'ACCOUNT_HOLDER' ? 'account-holder' :
                     role === 'BROKER' ? 'broker' : 'admin';
  
  return `/${rolePrefix}/${domain}`;
}

/**
 * Gets the default landing page URL for a specific role
 * @param role - The user's role (ACCOUNT_HOLDER, BROKER, ADMIN)
 * @returns The default landing page URL for the role
 */
export function getDefaultRoleUrl(role: UserRole): string {
  return getRoleBasedUrl(role, 'policies');
}

/**
 * Gets the role display name in Spanish for UI purposes
 * @param role - The user's role (ACCOUNT_HOLDER, BROKER, ADMIN)
 * @returns The Spanish display name for the role
 */
export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case "ACCOUNT_HOLDER":
      return "Asegurado";
    case "BROKER":
      return "Corredor";
    case "ADMIN":
      return "Administrador";
    default:
      return "Plataforma de Seguros";
  }
}
