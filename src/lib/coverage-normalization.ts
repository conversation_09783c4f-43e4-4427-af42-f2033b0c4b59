/**
 * Coverage normalization utilities for RCO (Reglamento de Control de Obligaciones)
 * Handles automatic normalization of coverage data according to Spanish insurance regulations
 */

import { GuaranteeType } from '@prisma/client';

/**
 * Normalizes mandatory liability coverage to comply with RCO regulations
 * Sets standard caps: 70M EUR for bodily injury, 15M EUR for property damage
 */
function normalizeMandatoryLiability(data: any) {
  if (data.type === GuaranteeType.MANDATORY_LIABILITY) {
    return {
      ...data,
      limitIsUnlimited: false,
      limitIsFullCost: false,
      liabilityBodilyCap: 70000000, // 70M EUR
      liabilityPropertyCap: 15000000, // 15M EUR
      deductiblePercent: 0 // No deductible for mandatory liability
    };
  }
  return data;
}

/**
 * Normalizes coverage data for create operations
 * Applies RCO regulations and sets appropriate defaults
 */
export function normalizeCoverageForCreate(data: any) {
  let normalized = { ...data };
  
  // Apply mandatory liability normalization
  normalized = normalizeMandatoryLiability(normalized);
  
  // Set default values for optional fields if not provided
  if (normalized.limitIsUnlimited === undefined) {
    normalized.limitIsUnlimited = false;
  }
  if (normalized.limitIsFullCost === undefined) {
    normalized.limitIsFullCost = false;
  }
  if (normalized.deductiblePercent === undefined) {
    normalized.deductiblePercent = 0;
  }
  
  return normalized;
}

/**
 * Normalizes bid coverage data for create operations
 * Applies same RCO regulations as regular coverage
 */
export function normalizeBidCoverageForCreate(data: any) {
  return normalizeCoverageForCreate(data);
}

/**
 * Normalizes coverage data for update operations
 * Only normalizes fields that are being updated
 */
export function normalizeCoverageForUpdate(data: any) {
  let normalized = { ...data };
  
  // Apply mandatory liability normalization if type is being updated
  if (data.type !== undefined) {
    normalized = normalizeMandatoryLiability(normalized);
  }
  
  return normalized;
}

/**
 * Normalizes bid coverage data for update operations
 * Applies same logic as regular coverage updates
 */
export function normalizeBidCoverageForUpdate(data: any) {
  return normalizeCoverageForUpdate(data);
}